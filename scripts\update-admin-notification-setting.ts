#!/usr/bin/env tsx

/**
 * <PERSON><PERSON><PERSON> to add admin notification email setting to business_settings table
 * Run with: npx tsx scripts/update-admin-notification-setting.ts
 */

import { supabase } from "../lib/supabase";

async function updateAdminNotificationSetting() {
	console.log("Adding admin notification email setting...");

	try {
		const { data, error } = await supabase
			.from("business_settings")
			.upsert({
				key: "admin_notification_email",
				value: "<EMAIL>",
				value_type: "string",
				category: "notifications",
				description: "Email address for admin notifications (new reservations, payments, etc.)",
				is_public: false,
			})
			.select();

		if (error) {
			console.error("Error updating setting:", error);
			process.exit(1);
		}

		console.log("✅ Admin notification email setting added successfully:", data);
		console.log("Setting details:");
		console.log("- Key: admin_notification_email");
		console.log("- Value: <EMAIL>");
		console.log("- Category: notifications");
		console.log("- Description: Email address for admin notifications");
		
		process.exit(0);
	} catch (error) {
		console.error("Script error:", error);
		process.exit(1);
	}
}

updateAdminNotificationSetting();
