import { withAdminAuth } from "@/lib/admin-auth";
import { supabaseAdmin } from "@/lib/supabase";
import { NextRequest, NextResponse } from "next/server";

// GET /api/admin/reservations/[id]/payment-status - Get payment status for a reservation
export const GET = withAdminAuth(async (request: NextRequest, user, { params }: { params: { id: string } }) => {
	try {
		const reservationId = params.id;

		if (!reservationId) {
			return NextResponse.json({ error: "Reservation ID is required" }, { status: 400 });
		}

		if (!supabaseAdmin) {
			return NextResponse.json({ error: "Database connection not available" }, { status: 500 });
		}

		// Get reservation details
		const { data: reservation, error: reservationError } = await supabaseAdmin
			.from("reservations")
			.select(
				`
        id,
        total_amount,
        deposit_amount,
        remaining_amount,
        deposit_paid,
        status,
        reservation_number,
        customer:customers(first_name, last_name, email),
        service:services(name)
      `
			)
			.eq("id", reservationId)
			.single();

		if (reservationError || !reservation) {
			return NextResponse.json({ error: "Reservation not found" }, { status: 404 });
		}

		// Get all payments for this reservation
		const { data: payments, error: paymentsError } = await supabaseAdmin
			.from("payments")
			.select(
				`
        *,
        completed_by_profile:profiles!completed_by(first_name, last_name)
      `
			)
			.eq("reservation_id", reservationId)
			.order("created_at", { ascending: false });

		if (paymentsError) {
			console.error("Error fetching payments:", paymentsError);
			return NextResponse.json({ error: "Failed to fetch payments" }, { status: 500 });
		}

		// Calculate payment summary
		const successfulPayments = payments?.filter((p: any) => p.status === "completed") || [];
		const totalPaid = successfulPayments.reduce((sum: number, p: any) => sum + p.amount, 0);
		const remainingAmount = reservation.total_amount - totalPaid;

		const hasDepositPayment = successfulPayments.some((p: any) => p.is_deposit);
		const hasFullPayment = successfulPayments.some((p: any) => p.payment_type === "full");
		const hasManualPayments = successfulPayments.some((p: any) => p.completed_manually);

		const pendingPayments =
			payments?.filter((p: any) => ["pending", "processing", "requires_action"].includes(p.status)) || [];

		const failedPayments =
			payments?.filter((p: any) => ["failed", "canceled", "requires_payment_method"].includes(p.status)) || [];

		// Determine payment status
		let paymentStatus = "unpaid";
		if (hasFullPayment || remainingAmount <= 0) {
			paymentStatus = "fully_paid";
		} else if (hasDepositPayment) {
			paymentStatus = "deposit_paid";
		} else if (totalPaid > 0) {
			paymentStatus = "partial_paid";
		}

		// Check if remaining payment can be marked as complete
		const canMarkComplete = remainingAmount > 0 && !pendingPayments.length;

		return NextResponse.json({
			success: true,
			reservation: {
				id: reservation.id,
				reservationNumber: reservation.reservation_number,
				totalAmount: reservation.total_amount,
				customerName: `${reservation.customer.first_name} ${reservation.customer.last_name}`,
				serviceName: reservation.service.name,
				status: reservation.status,
			},
			paymentSummary: {
				totalAmount: reservation.total_amount,
				totalPaid,
				remainingAmount,
				paymentStatus,
				hasDepositPayment,
				hasFullPayment,
				hasManualPayments,
				canMarkComplete,
			},
			payments: successfulPayments.map((p) => ({
				id: p.id,
				amount: p.amount,
				currency: p.currency,
				paymentType: p.payment_type,
				isDeposit: p.is_deposit,
				paymentMethod: p.payment_method,
				paymentDate: p.payment_date,
				completedManually: p.completed_manually,
				completedBy: p.completed_by_profile
					? `${p.completed_by_profile.first_name} ${p.completed_by_profile.last_name}`
					: null,
				completionNotes: p.completion_notes,
				paymentIntentId: p.payment_intent_id,
			})),
			pendingPayments: pendingPayments.map((p) => ({
				id: p.id,
				amount: p.amount,
				status: p.status,
				paymentType: p.payment_type,
				createdAt: p.created_at,
			})),
			failedPayments: failedPayments.map((p) => ({
				id: p.id,
				amount: p.amount,
				status: p.status,
				failureReason: p.failure_reason,
				createdAt: p.created_at,
			})),
		});
	} catch (error) {
		console.error("Error getting payment status:", error);
		return NextResponse.json({ error: "Internal server error" }, { status: 500 });
	}
});
