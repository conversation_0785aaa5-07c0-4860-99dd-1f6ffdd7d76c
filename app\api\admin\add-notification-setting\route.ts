import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/lib/supabase";

export async function POST(request: NextRequest) {
	try {
		console.log("Adding admin notification email setting...");

		const { data, error } = await supabase
			.from("business_settings")
			.upsert({
				key: "admin_notification_email",
				value: "<EMAIL>",
				value_type: "string",
				category: "notifications",
				description: "Email address for admin notifications (new reservations, payments, etc.)",
				is_public: false,
			})
			.select();

		if (error) {
			console.error("Error adding setting:", error);
			return NextResponse.json({ 
				success: false, 
				error: error.message 
			}, { status: 500 });
		}

		console.log("✅ Admin notification email setting added successfully:", data);
		
		return NextResponse.json({
			success: true,
			message: "Admin notification email setting added successfully",
			data: data[0]
		});

	} catch (error) {
		console.error("API error:", error);
		return NextResponse.json({
			success: false,
			error: error instanceof Error ? error.message : "Unknown error"
		}, { status: 500 });
	}
}
