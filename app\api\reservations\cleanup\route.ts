import { NextRequest, NextResponse } from "next/server";
import { getSupabaseAdmin } from "@/lib/supabase";

/**
 * API endpoint to immediately cleanup abandoned reservations
 * Called when users navigate away from payment page or close browser
 */
export async function POST(request: NextRequest) {
	try {
		const { reservationId } = await request.json();

		if (!reservationId) {
			return NextResponse.json({ error: "Reservation ID is required" }, { status: 400 });
		}

		console.log("🧹 [CLEANUP] Starting cleanup for reservation:", reservationId);

		// Check if reservation exists and is still pending
		console.log("🧹 [CLEANUP] Fetching reservation status...");
		const supabaseAdmin = getSupabaseAdmin();
		const { data: reservation, error: fetchError } = await supabaseAdmin
			.from("reservations")
			.select("status")
			.eq("id", reservationId)
			.maybeSingle(); // Use maybeSingle() instead of single() to handle missing records

		if (fetchError) {
			console.error("🧹 [CLEANUP] Error fetching reservation:", fetchError);
			return NextResponse.json(
				{
					error: "Database error",
				},
				{ status: 500 }
			);
		}

		// If reservation doesn't exist or is not pending, consider it already cleaned up
		if (!reservation) {
			console.log("🧹 [CLEANUP] Reservation not found, likely already cleaned up");
			return NextResponse.json({
				success: true,
				message: "Reservation already cleaned up or doesn't exist",
			});
		}

		console.log("🧹 [CLEANUP] Current reservation status:", reservation.status);

		// If reservation is confirmed, don't clean it up
		if (reservation.status === "confirmed") {
			console.log(`🧹 [CLEANUP] Reservation is confirmed, no cleanup needed`);
			return NextResponse.json({
				success: true,
				message: `Reservation is confirmed, no cleanup needed`,
			});
		}

		// For pending or cancelled reservations, clean up equipment reservations
		let reservationNeedsUpdate = false;
		if (reservation.status === "pending") {
			reservationNeedsUpdate = true;
			console.log("🧹 [CLEANUP] Reservation is pending, will cancel it");
		} else {
			console.log(`🧹 [CLEANUP] Reservation is ${reservation.status}, will clean up equipment only`);
		}

		// Cancel the reservation only if it's pending
		if (reservationNeedsUpdate) {
			console.log("🧹 [CLEANUP] Cancelling pending reservation...");
			const { error: updateError } = await supabaseAdmin
				.from("reservations")
				.update({
					status: "cancelled",
					admin_notes: "Automatically cancelled - user navigated away from payment",
					updated_at: new Date().toISOString(),
				})
				.eq("id", reservationId);

			if (updateError) {
				console.error("🧹 [CLEANUP] Error cancelling reservation:", updateError);
				return NextResponse.json(
					{
						error: "Failed to cancel reservation",
					},
					{ status: 500 }
				);
			}
			console.log("🧹 [CLEANUP] ✅ Reservation cancelled successfully");
		}

		// Clean up equipment reservations by deleting them
		console.log("🧹 [CLEANUP] Cleaning up equipment reservations...");
		const { data: deletedEquipment, error: equipmentError } = await supabaseAdmin
			.from("equipment_reservations")
			.delete()
			.eq("reservation_id", reservationId)
			.select("id");

		if (equipmentError) {
			console.error("🧹 [CLEANUP] Error deleting equipment reservations:", equipmentError);
			// Don't fail the request, as the main reservation was already cancelled
		} else {
			console.log(`🧹 [CLEANUP] ✅ Deleted ${deletedEquipment?.length || 0} equipment reservations`);
		}

		const cleanupMessage = reservationNeedsUpdate
			? "Reservation cancelled and equipment cleaned up"
			: "Equipment reservations cleaned up";

		console.log("🧹 [CLEANUP] ✅ Successfully cleaned up reservation:", reservationId);

		return NextResponse.json({
			success: true,
			message: cleanupMessage,
		});
	} catch (error) {
		console.error("Reservation cleanup error:", error);
		return NextResponse.json(
			{
				error: "Internal server error",
				details: error instanceof Error ? error.message : "Unknown error",
			},
			{ status: 500 }
		);
	}
}
