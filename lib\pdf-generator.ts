import jsPDF from 'jspdf';
import { generateBookingQRCode, BookingQRData } from './qr-code';

export interface BookingPDFData {
  reservationNumber: string;
  customerName: string;
  email: string;
  phone: string;
  serviceName: string;
  date: string;
  time: string;
  participants: number;
  totalAmount: number;
  qrCodeData: BookingQRData;
  specialRequests?: string;
}

/**
 * Generate PDF for booking confirmation
 */
export async function generateBookingConfirmationPDF(data: BookingPDFData): Promise<Blob> {
  try {
    // Create new PDF document
    const pdf = new jsPDF({
      orientation: 'portrait',
      unit: 'mm',
      format: 'a4',
    });

    // Set font
    pdf.setFont('helvetica');

    // Header
    pdf.setFontSize(24);
    pdf.setTextColor(16, 185, 129); // Emerald color
    pdf.text('Soleil & Découverte', 20, 30);
    
    pdf.setFontSize(12);
    pdf.setTextColor(100, 100, 100);
    pdf.text('Excursions éco-responsables en Guadeloupe', 20, 38);

    // Title
    pdf.setFontSize(18);
    pdf.setTextColor(0, 0, 0);
    pdf.text('Confirmation de Réservation', 20, 55);

    // Reservation details box
    pdf.setDrawColor(16, 185, 129);
    pdf.setLineWidth(0.5);
    pdf.rect(20, 65, 170, 40);

    pdf.setFontSize(14);
    pdf.setTextColor(16, 185, 129);
    pdf.text('Détails de la Réservation', 25, 75);

    pdf.setFontSize(11);
    pdf.setTextColor(0, 0, 0);
    pdf.text(`Numéro de réservation: ${data.reservationNumber}`, 25, 85);
    pdf.text(`Service: ${data.serviceName}`, 25, 92);
    pdf.text(`Date: ${data.date}`, 25, 99);
    pdf.text(`Heure: ${data.time}`, 100, 99);

    // Customer information
    pdf.setFontSize(14);
    pdf.setTextColor(16, 185, 129);
    pdf.text('Informations Client', 25, 120);

    pdf.setFontSize(11);
    pdf.setTextColor(0, 0, 0);
    pdf.text(`Nom: ${data.customerName}`, 25, 130);
    pdf.text(`Email: ${data.email}`, 25, 137);
    pdf.text(`Téléphone: ${data.phone}`, 25, 144);
    pdf.text(`Participants: ${data.participants}`, 25, 151);

    // Amount
    pdf.setFontSize(14);
    pdf.setTextColor(16, 185, 129);
    pdf.text('Montant Total', 25, 165);

    pdf.setFontSize(16);
    pdf.setTextColor(0, 0, 0);
    pdf.text(`${data.totalAmount}€`, 25, 175);

    // Special requests if any
    if (data.specialRequests) {
      pdf.setFontSize(14);
      pdf.setTextColor(16, 185, 129);
      pdf.text('Demandes Spéciales', 25, 190);

      pdf.setFontSize(11);
      pdf.setTextColor(0, 0, 0);
      const splitRequests = pdf.splitTextToSize(data.specialRequests, 160);
      pdf.text(splitRequests, 25, 200);
    }

    // Generate QR code
    const qrCodeDataURL = await generateBookingQRCode(data.qrCodeData);
    
    // Add QR code to PDF
    const qrY = data.specialRequests ? 220 : 190;
    pdf.setFontSize(14);
    pdf.setTextColor(16, 185, 129);
    pdf.text('Code QR de Vérification', 25, qrY);

    // Convert data URL to image and add to PDF
    pdf.addImage(qrCodeDataURL, 'PNG', 25, qrY + 5, 40, 40);

    pdf.setFontSize(10);
    pdf.setTextColor(100, 100, 100);
    pdf.text('Scannez ce code pour vérifier votre réservation', 25, qrY + 50);

    // Important information
    const infoY = qrY + 60;
    pdf.setFontSize(14);
    pdf.setTextColor(16, 185, 129);
    pdf.text('Informations Importantes', 25, infoY);

    pdf.setFontSize(10);
    pdf.setTextColor(0, 0, 0);
    const importantInfo = [
      '• Présentez-vous 15 minutes avant l\'heure prévue',
      '• Apportez une pièce d\'identité',
      '• Prévoyez des vêtements adaptés à l\'activité',
      '• En cas d\'annulation, contactez-nous 24h à l\'avance',
      '• Pour toute question: <EMAIL>'
    ];

    let currentY = infoY + 8;
    importantInfo.forEach(info => {
      pdf.text(info, 25, currentY);
      currentY += 6;
    });

    // Footer
    pdf.setFontSize(8);
    pdf.setTextColor(150, 150, 150);
    pdf.text('Soleil & Découverte - Excursions éco-responsables', 20, 280);
    pdf.text('Guadeloupe, France', 20, 285);
    pdf.text(`Généré le ${new Date().toLocaleDateString('fr-FR')}`, 150, 285);

    // Convert to blob
    const pdfBlob = pdf.output('blob');
    return pdfBlob;

  } catch (error) {
    console.error('Error generating PDF:', error);
    throw new Error('Failed to generate PDF');
  }
}

/**
 * Download PDF file
 */
export function downloadPDF(blob: Blob, filename: string): void {
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
}
