/** @type {import('next').NextConfig} */
const nextConfig = {
	eslint: {
		ignoreDuringBuilds: true,
	},
	typescript: {
		ignoreBuildErrors: true,
	},
	images: {
		domains: [
			"localhost",
			"http://localhost:3000",
			"www.soleiletdecouverte.com",
			"soleiletdecouverte.com",
			"https://soleil-et-decouverte.vercel.app",
			"zalzjvuxoffmhaokvzda.supabase.co",
		],
		formats: ["image/webp", "image/avif"],
		deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
		imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
	},
	experimental: {
		// optimizeCss: true, // Disabled due to critters dependency issue
	},
	// Force development mode for debugging
	// env: {
	//	NODE_ENV: "development",
	// },
	// Enable detailed error messages
	productionBrowserSourceMaps: true,
	// Don't minify to see better error messages
	swcMinify: false,
	compress: false,
	poweredByHeader: false,
	generateEtags: true,
	trailingSlash: false,
};

export default nextConfig;
