# Storage Setup for Service Images

## Supabase Storage Bucket Setup

To enable image uploads for services, you need to create a storage bucket in Supabase.

### 1. Create Storage Bucket

1. Go to your Supabase project dashboard
2. Navigate to **Storage** in the sidebar
3. Click **Create a new bucket**
4. Use the following settings:
   - **Name**: `service-images`
   - **Public bucket**: ✅ Enabled
   - **File size limit**: 5MB
   - **Allowed MIME types**: `image/jpeg, image/png, image/gif, image/webp`

### 2. Set up RLS Policies

Go to **Storage** > **Policies** and create the following policies for the `service-images` bucket:

#### Policy 1: Allow public read access
- **Policy name**: `Allow public read access`
- **Allowed operation**: `SELECT`
- **Policy definition**: `true`

#### Policy 2: Allow authenticated users to upload
- **Policy name**: `Allow authenticated users to upload`
- **Allowed operation**: `INSERT`
- **Policy definition**: `auth.role() = 'authenticated'`

#### Policy 3: Allow authenticated users to update
- **Policy name**: `Allow authenticated users to update their uploads`
- **Allowed operation**: `UPDATE`
- **Policy definition**: `auth.role() = 'authenticated'`

#### Policy 4: Allow authenticated users to delete
- **Policy name**: `Allow authenticated users to delete their uploads`
- **Allowed operation**: `DELETE`
- **Policy definition**: `auth.role() = 'authenticated'`

### 3. Alternative: SQL Setup

You can also run this SQL in the Supabase SQL editor:

```sql
-- Create the storage bucket (if not exists)
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'service-images',
  'service-images',
  true,
  5242880,
  ARRAY['image/jpeg', 'image/png', 'image/gif', 'image/webp']
) ON CONFLICT (id) DO NOTHING;

-- Create RLS policies
CREATE POLICY "Allow public read access" ON storage.objects
  FOR SELECT USING (bucket_id = 'service-images');

CREATE POLICY "Allow authenticated users to upload" ON storage.objects
  FOR INSERT WITH CHECK (bucket_id = 'service-images' AND auth.role() = 'authenticated');

CREATE POLICY "Allow authenticated users to update" ON storage.objects
  FOR UPDATE USING (bucket_id = 'service-images' AND auth.role() = 'authenticated');

CREATE POLICY "Allow authenticated users to delete" ON storage.objects
  FOR DELETE USING (bucket_id = 'service-images' AND auth.role() = 'authenticated');
```

## Features

Once set up, the image upload functionality provides:

- ✅ **Drag & drop** or click to upload
- ✅ **Image preview** with remove option
- ✅ **File validation** (type and size)
- ✅ **Progress indicators** during upload
- ✅ **Error handling** with user-friendly messages
- ✅ **Automatic file naming** with timestamps
- ✅ **Public URL generation** for immediate use

## Usage

The `ImageUpload` component is now integrated into:
- Service creation form (`AdminServices`)
- Service edit form (`ServiceEdit`)

Images are automatically uploaded to Supabase Storage and the public URL is saved to the service's `image_url` field.
