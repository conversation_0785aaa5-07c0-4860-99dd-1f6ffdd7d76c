import { NextRequest, NextResponse } from "next/server";
import { supabaseAdmin } from "@/lib/supabase";

/**
 * Cron job to clean up abandoned pending reservations
 * This endpoint is called by Vercel Cron daily at 2 AM
 *
 * Cancels pending reservations that are older than 30 minutes
 * to prevent them from blocking available time slots
 */
export async function GET(request: NextRequest) {
	try {
		console.log("=== ABANDONED RESERVATIONS CLEANUP CRON JOB STARTED ===");

		// Verify this is a legitimate cron request
		const authHeader = request.headers.get("authorization");
		const cronSecret = process.env.CRON_SECRET;

		if (cronSecret && authHeader !== `Bearer ${cronSecret}`) {
			console.log("Unauthorized cron request");
			return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
		}

		// Calculate cutoff time (30 minutes ago)
		const cutoffTime = new Date(Date.now() - 30 * 60 * 1000);

		console.log("Looking for pending reservations older than:", cutoffTime.toISOString());

		// Find pending reservations older than 30 minutes
		const { data: abandonedReservations, error: fetchError } = await supabaseAdmin
			.from("reservations")
			.select("id, reservation_number, created_at, customer_id")
			.eq("status", "pending")
			.lt("created_at", cutoffTime.toISOString());

		if (fetchError) {
			console.error("Error fetching abandoned reservations:", fetchError);
			return NextResponse.json({ error: "Database error" }, { status: 500 });
		}

		if (!abandonedReservations || abandonedReservations.length === 0) {
			console.log("No abandoned reservations found");
			return NextResponse.json({
				success: true,
				message: "No abandoned reservations to clean up",
				cleaned: 0,
			});
		}

		console.log(`Found ${abandonedReservations.length} abandoned reservations to clean up`);

		const reservationIds = abandonedReservations.map((r) => r.id);
		let successCount = 0;
		let errorCount = 0;
		const errors: string[] = [];

		// Cancel the abandoned reservations
		const { error: updateError } = await supabaseAdmin
			.from("reservations")
			.update({
				status: "cancelled",
				admin_notes: "Automatically cancelled - payment abandoned after 30 minutes",
				updated_at: new Date().toISOString(),
			})
			.in("id", reservationIds);

		if (updateError) {
			console.error("Error cancelling abandoned reservations:", updateError);
			return NextResponse.json({ error: "Failed to cancel reservations" }, { status: 500 });
		}

		successCount = reservationIds.length;

		// Also cancel any pending payments for these reservations
		const { error: paymentsError } = await supabaseAdmin
			.from("payments")
			.update({
				status: "abandoned",
				failure_reason: "Reservation automatically cancelled after 30 minutes",
				updated_at: new Date().toISOString(),
			})
			.in("reservation_id", reservationIds)
			.in("status", ["pending", "processing", "requires_action"]);

		if (paymentsError) {
			console.error("Error updating payment status:", paymentsError);
			// Don't fail the request, as the reservations were already cancelled
		}

		// Clean up equipment reservations for cancelled reservations
		const { error: equipmentError } = await supabaseAdmin
			.from("equipment_reservations")
			.update({
				status: "cancelled",
				updated_at: new Date().toISOString(),
			})
			.in("reservation_id", reservationIds);

		if (equipmentError) {
			console.error("Error updating equipment reservations:", equipmentError);
			// Don't fail the request, as the main reservations were already cancelled
		}

		console.log("=== ABANDONED RESERVATIONS CLEANUP COMPLETED ===");
		console.log(`Cleaned up: ${successCount} reservations`);

		return NextResponse.json({
			success: true,
			message: "Abandoned reservations cleanup completed",
			cleaned: successCount,
			reservations: abandonedReservations.map((r) => ({
				id: r.id,
				reservation_number: r.reservation_number,
				created_at: r.created_at,
			})),
		});
	} catch (error) {
		console.error("Cleanup cron job error:", error);
		return NextResponse.json(
			{
				error: "Internal server error",
				details: error instanceof Error ? error.message : "Unknown error",
			},
			{ status: 500 }
		);
	}
}

/**
 * Manual trigger for testing (POST request)
 */
export async function POST(request: NextRequest) {
	try {
		console.log("=== MANUAL ABANDONED RESERVATIONS CLEANUP TRIGGER ===");

		// For manual testing, use a shorter cutoff (5 minutes)
		const cutoffTime = new Date(Date.now() - 5 * 60 * 1000);

		const { data: abandonedReservations, error } = await supabaseAdmin
			.from("reservations")
			.select("id, reservation_number, created_at")
			.eq("status", "pending")
			.lt("created_at", cutoffTime.toISOString());

		if (error) {
			return NextResponse.json({ error: "Database error" }, { status: 500 });
		}

		return NextResponse.json({
			success: true,
			message: "Manual cleanup trigger completed",
			found: abandonedReservations?.length || 0,
			reservations: abandonedReservations || [],
		});
	} catch (error) {
		console.error("Manual cleanup trigger error:", error);
		return NextResponse.json(
			{
				error: "Internal server error",
				details: error instanceof Error ? error.message : "Unknown error",
			},
			{ status: 500 }
		);
	}
}
