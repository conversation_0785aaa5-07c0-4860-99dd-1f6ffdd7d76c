#!/usr/bin/env node

// Employee creation script for Soleil et Découverte
// Creates entries in users (auth), profiles, and employees tables
// Run with: node scripts/create-employee.js

const { createClient } = require("@supabase/supabase-js");

// Replace these with your actual values from .env.local
const SUPABASE_URL = "https://zalzjvuxoffmhaokvzda.supabase.co";
const SUPABASE_SERVICE_KEY =
	"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InphbHpqdnV4b2ZmbWhhb2t2emRhIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MjYyMDU2MywiZXhwIjoyMDY4MTk2NTYzfQ.3UyRaKx1aoBTDQXJazne99UaoXVa5IfKK5S_oCkwtVI";

const readline = require("readline");

// Function to prompt for user input
function askQuestion(question) {
	const rl = readline.createInterface({
		input: process.stdin,
		output: process.stdout,
	});

	return new Promise((resolve) => {
		rl.question(question, (answer) => {
			rl.close();
			resolve(answer);
		});
	});
}

// Function to generate employee code
function generateEmployeeCode(firstName, lastName) {
	const initials = `${firstName[0]}${lastName[0]}`.toUpperCase();
	const timestamp = Date.now().toString().slice(-4);
	return `EMP-${initials}-${timestamp}`;
}

async function createEmployee() {
	console.log("👷 Creating employee user...");
	console.log("");

	// Prompt for employee details
	const EMPLOYEE_EMAIL = await askQuestion("Enter employee email: ");
	if (!EMPLOYEE_EMAIL) {
		console.error("❌ Email is required");
		process.exit(1);
	}

	const EMPLOYEE_PASSWORD = (await askQuestion("Enter employee password (default: employee123): ")) || "employee123";
	const FIRST_NAME = await askQuestion("Enter first name: ");
	if (!FIRST_NAME) {
		console.error("❌ First name is required");
		process.exit(1);
	}

	const LAST_NAME = await askQuestion("Enter last name: ");
	if (!LAST_NAME) {
		console.error("❌ Last name is required");
		process.exit(1);
	}

	const PHONE = await askQuestion("Enter phone number (optional): ");
	const ROLE = (await askQuestion("Enter role (employee/manager, default: employee): ")) || "employee";

	console.log("");
	console.log(`Creating employee: ${FIRST_NAME} ${LAST_NAME} (${EMPLOYEE_EMAIL})`);
	console.log(`Role: ${ROLE}`);
	console.log("");

	try {
		// Create Supabase client with service role
		const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY, {
			auth: {
				autoRefreshToken: false,
				persistSession: false,
			},
		});

		console.log("📧 Creating auth user...");

		// Create user in Supabase Auth
		const { data: authData, error: authError } = await supabase.auth.admin.createUser({
			email: EMPLOYEE_EMAIL,
			password: EMPLOYEE_PASSWORD,
			email_confirm: true,
			user_metadata: {
				first_name: FIRST_NAME,
				last_name: LAST_NAME,
				role: ROLE,
			},
		});

		if (authError) {
			console.error("❌ Auth error:", authError.message);
			process.exit(1);
		}

		if (!authData.user) {
			console.error("❌ Failed to create user");
			process.exit(1);
		}

		console.log("✅ Auth user created successfully!");
		console.log("👤 Creating profile...");

		// Create profile entry
		const { data: profileData, error: profileError } = await supabase
			.from("profiles")
			.insert({
				id: authData.user.id,
				email: EMPLOYEE_EMAIL,
				first_name: FIRST_NAME,
				last_name: LAST_NAME,
				phone: PHONE || null,
				role: ROLE,
			})
			.select()
			.single();

		if (profileError) {
			console.error("❌ Profile error:", profileError.message);
			// Try to clean up auth user
			await supabase.auth.admin.deleteUser(authData.user.id);
			process.exit(1);
		}

		console.log("✅ Profile created successfully!");
		console.log("👷 Creating employee record...");

		// Generate employee code
		const employeeCode = generateEmployeeCode(FIRST_NAME, LAST_NAME);

		// Create employee entry
		const { data: employeeData, error: employeeError } = await supabase
			.from("employees")
			.insert({
				id: authData.user.id, // Link to auth user
				employee_code: employeeCode,
				first_name: FIRST_NAME,
				last_name: LAST_NAME,
				email: EMPLOYEE_EMAIL,
				phone: PHONE || null,
				role: ROLE,
				hire_date: new Date().toISOString(),
				is_active: true,
			})
			.select()
			.single();

		if (employeeError) {
			console.error("❌ Employee error:", employeeError.message);
			// Try to clean up auth user and profile
			await supabase.auth.admin.deleteUser(authData.user.id);
			await supabase.from("profiles").delete().eq("id", authData.user.id);
			process.exit(1);
		}

		console.log("✅ Employee record created successfully!");
		console.log("");
		console.log("📋 Employee Details:");
		console.log(`   Name: ${FIRST_NAME} ${LAST_NAME}`);
		console.log(`   Email: ${EMPLOYEE_EMAIL}`);
		console.log(`   Password: ${EMPLOYEE_PASSWORD}`);
		console.log(`   Role: ${ROLE}`);
		console.log(`   Employee Code: ${employeeCode}`);
		console.log("");
		console.log("🌐 Employee can now login at: http://localhost:3000/login");
		console.log("🔧 Admin can assign services to this employee in the admin panel");
		console.log("");
		console.log("📝 Next steps:");
		console.log("   1. Set up employee availability schedule");
		console.log("   2. Assign service qualifications");
		console.log("   3. Configure working hours");
	} catch (error) {
		console.error("❌ Unexpected error:", error);
		process.exit(1);
	}
}

// Run the script
createEmployee().catch(console.error);
