-- Migration: Modify service_scheduling_rules to support multiple days
-- This migration changes day_of_week from a single integer to an array of integers

-- Step 1: Add new column for multiple days
ALTER TABLE service_scheduling_rules 
ADD COLUMN days_of_week integer[] DEFAULT NULL;

-- Step 2: Migrate existing data
-- Convert single day_of_week values to array format
UPDATE service_scheduling_rules 
SET days_of_week = CASE 
    WHEN day_of_week IS NULL THEN NULL  -- Keep NULL for "all days"
    ELSE ARRAY[day_of_week]             -- Convert single day to array
END;

-- Step 3: Drop the old column
ALTER TABLE service_scheduling_rules 
DROP COLUMN day_of_week;

-- Step 4: Add constraint to ensure valid day values (0-6 for Sunday-Saturday)
ALTER TABLE service_scheduling_rules 
ADD CONSTRAINT valid_days_of_week 
CHECK (
    days_of_week IS NULL OR 
    (
        array_length(days_of_week, 1) > 0 AND
        NOT EXISTS (
            SELECT 1 FROM unnest(days_of_week) AS day 
            WHERE day < 0 OR day > 6
        )
    )
);

-- Step 5: Add index for better query performance
CREATE INDEX idx_service_scheduling_rules_days_of_week 
ON service_scheduling_rules USING GIN (days_of_week);

-- Step 6: Update the types for TypeScript generation
COMMENT ON COLUMN service_scheduling_rules.days_of_week IS 
'Array of days of week (0=Sunday, 1=Monday, ..., 6=Saturday). NULL means all days.';
