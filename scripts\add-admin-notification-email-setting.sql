-- Add admin notification email setting to business_settings table
INSERT INTO business_settings (
    key,
    value,
    value_type,
    category,
    description,
    is_public,
    created_at,
    updated_at
) VALUES (
    'admin_notification_email',
    '<EMAIL>',
    'string',
    'notifications',
    'Email address for admin notifications (new reservations, payments, etc.)',
    false,
    NOW(),
    NOW()
) ON CONFLICT (key) DO UPDATE SET
    value = EXCLUDED.value,
    description = EXCLUDED.description,
    category = EXCLUDED.category,
    updated_at = NOW();
