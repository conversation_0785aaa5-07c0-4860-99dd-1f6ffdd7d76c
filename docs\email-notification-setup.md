# Email Notification System Setup Guide

This guide provides comprehensive instructions for setting up and configuring the email notification system for the Soleil & Découverte reservation application.

## Overview

The email notification system provides automated email communications for:

-   ✅ **Booking confirmation emails** - Sent when reservations are created
-   ✅ **Payment confirmation emails** - Sent when payments are processed
-   ✅ **Invoice generation and delivery** - PDF invoices sent after payment
-   ✅ **Cancellation notifications** - Sent when reservations are cancelled
-   ✅ **Reminder emails** - Automated 24-hour reminders before appointments
-   ✅ **Admin notifications** - Staff alerts for new reservations, payments, and cancellations

### 2. Domain Configuration

1. In the Resend dashboard, go to **Domains**
2. Add your domain: `soleiletdecouverte.com`
3. Configure DNS records as provided by Resend:
    - Add the TXT record for domain verification
    - Add MX records for email delivery
    - Add DKIM records for authentication

### 3. API Key Generation

1. Go to **API Keys** in the Resend dashboard
2. Click **Create API Key**
3. Name it "Soleil et Decouverte Production" (or similar)
4. Copy the API key and add it to your environment variables

### 4. Sender Identity

1. Go to **Domains** and verify your domain is active
2. The from email should be: `<EMAIL>`
3. Ensure this matches your `RESEND_FROM_EMAIL` environment variable

## Automated Reminder System Setup

### Option 1: Vercel Cron (Recommended for Vercel deployments)

1. Create `vercel.json` in your project root:

```json
{
	"crons": [
		{
			"path": "/api/cron/send-reminders",
			"schedule": "0 * * * *"
		}
	]
}
```

2. Deploy to Vercel - cron jobs will run automatically

## Email Templates Customization

### Template Locations

-   **Customer emails**: `lib/email-service.ts`
-   **Admin emails**: `lib/email-templates/admin-templates.ts`
-   **Deposit emails**: `lib/email-templates/deposit-templates.ts`

### Customizing Templates

1. **Company Information**: Update company details in all templates
2. **Styling**: Modify CSS styles in the `<style>` sections
3. **Content**: Edit the HTML and text content as needed
4. **Languages**: Add translations for multi-language support

### Template Variables

Common variables available in templates:

-   `customerName` - Customer's full name
-   `reservationNumber` - Unique reservation identifier
-   `serviceName` - Name of the booked service
-   `date` - Service date (formatted)
-   `time` - Service time (formatted)
-   `participants` - Number of participants
-   `totalAmount` - Total cost
-   `specialRequests` - Customer's special requests

## Testing Email Functionality

### 1. Manual Testing

Test individual email functions:

```bash
# Test reminder system (manual trigger)
curl -X POST https://your-domain.com/api/cron/send-reminders

# Check for reservations needing reminders
curl https://your-domain.com/api/cron/send-reminders
```

### 2. Test Email Delivery

1. Create a test reservation
2. Process a test payment
3. Cancel a test reservation
4. Verify emails are received

### 3. Email Service Monitoring

Monitor email delivery in:

-   **Resend Dashboard**: Check delivery status, bounces, complaints
-   **Application Logs**: Check for email sending errors
-   **Database**: Verify `reminder_sent` flags are updated

## Troubleshooting

### Common Issues

1. **Emails not sending**

    - Check `RESEND_API_KEY` is correct
    - Verify domain is verified in Resend
    - Check application logs for errors

2. **Emails going to spam**

    - Ensure DKIM records are configured
    - Check SPF records
    - Monitor sender reputation in Resend

3. **Reminders not working**

    - Verify cron job is running
    - Check `CRON_SECRET` matches
    - Ensure database has reminder fields

4. **Template rendering issues**
    - Check for syntax errors in templates
    - Verify all variables are defined
    - Test with simple text templates first

### Debug Mode

Enable detailed logging by adding to your environment:

```bash
DEBUG_EMAILS=true
```

This will log all email attempts and responses.

## Security Considerations

1. **API Keys**: Never commit API keys to version control
2. **Cron Security**: Use a strong `CRON_SECRET` to prevent unauthorized access
3. **Email Content**: Sanitize user input in email templates
4. **Rate Limiting**: Monitor email sending rates to avoid service limits

### Resend Service Limits

-   **Free tier**: 3,000 emails/month
-   **Paid tiers**: Higher limits available
-   **Rate limits**: 10 emails/second

Monitor usage in the Resend dashboard and upgrade as needed.

## Additional Features

### Future Enhancements

Consider implementing:

-   **Email preferences** - Let customers choose notification types
-   **Multi-language support** - Templates in multiple languages
-   **Email analytics** - Track open rates and click-through rates
-   **Advanced scheduling** - More flexible reminder timing
-   **SMS notifications** - Backup communication channel
