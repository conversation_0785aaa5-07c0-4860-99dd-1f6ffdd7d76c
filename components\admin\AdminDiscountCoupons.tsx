"use client";

import { adminApi } from "@/lib/api-client";
import { DiscountCoupon, DiscountCouponInsert } from "@/lib/types";
import { AlertCircle, Edit, Loader2, Percent, Plus, Save, Tag, Trash2, X } from "lucide-react";
import { useEffect, useState } from "react";
import Button from "./ui/Button";

const AdminDiscountCoupons = () => {
	const [coupons, setCoupons] = useState<DiscountCoupon[]>([]);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);
	const [isCreating, setIsCreating] = useState(false);
	const [editingId, setEditingId] = useState<string | null>(null);
	const [editForm, setEditForm] = useState<Partial<DiscountCouponInsert>>({});
	const [saving, setSaving] = useState(false);

	useEffect(() => {
		fetchCoupons();
	}, []);

	const fetchCoupons = async () => {
		try {
			setLoading(true);
			setError(null);
			const response = await adminApi.getDiscountCoupons();
			if (response?.discountCoupons) {
				setCoupons(response.discountCoupons);
			}
		} catch (err) {
			console.error("Error fetching discount coupons:", err);
			setError("Erreur lors du chargement des codes de réduction");
		} finally {
			setLoading(false);
		}
	};

	const handleCreate = () => {
		setIsCreating(true);
		setEditForm({
			code: "",
			description: "",
			discount_type: "percentage",
			discount_value: 0,
			min_purchase_amount: undefined,
			max_discount_amount: undefined,
			usage_limit: undefined,
			valid_from: "",
			valid_until: "",
			applicable_services: [],
			is_active: true,
		});
	};

	const handleEdit = (coupon: DiscountCoupon) => {
		setEditingId(coupon.id);
		setEditForm({
			code: coupon.code,
			description: coupon.description || "",
			discount_type: coupon.discount_type,
			discount_value: coupon.discount_value,
			min_purchase_amount: coupon.min_purchase_amount || undefined,
			max_discount_amount: coupon.max_discount_amount || undefined,
			usage_limit: coupon.usage_limit || undefined,
			valid_from: coupon.valid_from ? coupon.valid_from.split("T")[0] : "",
			valid_until: coupon.valid_until ? coupon.valid_until.split("T")[0] : "",
			applicable_services: coupon.applicable_services || [],
			is_active: coupon.is_active,
		});
	};

	const handleSave = async () => {
		if (!editForm.code || !editForm.discount_type || editForm.discount_value === undefined) {
			setError("Veuillez remplir tous les champs obligatoires");
			return;
		}

		if (editForm.discount_type === "percentage" && editForm.discount_value > 100) {
			setError("La réduction en pourcentage ne peut pas dépasser 100%");
			return;
		}

		if (editForm.discount_value <= 0) {
			setError("La valeur de réduction doit être supérieure à 0");
			return;
		}

		try {
			setSaving(true);
			setError(null);

			const formData = {
				...editForm,
				valid_from: editForm.valid_from ? new Date(editForm.valid_from).toISOString() : null,
				valid_until: editForm.valid_until ? new Date(editForm.valid_until).toISOString() : null,
			};

			if (isCreating) {
				await adminApi.createDiscountCoupon(formData);
				setIsCreating(false);
			} else if (editingId) {
				await adminApi.updateDiscountCoupon(editingId, formData);
				setEditingId(null);
			}

			setEditForm({});
			await fetchCoupons(); // Refresh the list
		} catch (err) {
			console.error("Error saving discount coupon:", err);
			setError("Erreur lors de la sauvegarde du code de réduction");
		} finally {
			setSaving(false);
		}
	};

	const handleCancel = () => {
		setIsCreating(false);
		setEditingId(null);
		setEditForm({});
		setError(null);
	};

	const handleDelete = async (couponId: string) => {
		if (window.confirm("Êtes-vous sûr de vouloir supprimer ce code de réduction ?")) {
			try {
				await adminApi.deleteDiscountCoupon(couponId);
				await fetchCoupons(); // Refresh the list
			} catch (err) {
				console.error("Error deleting discount coupon:", err);
				setError("Erreur lors de la suppression du code de réduction");
			}
		}
	};

	const handleInputChange = (field: keyof DiscountCouponInsert, value: any) => {
		setEditForm((prev: Partial<DiscountCouponInsert>) => ({ ...prev, [field]: value }));
	};

	const formatCurrency = (amount: number) => {
		return new Intl.NumberFormat("fr-FR", {
			style: "currency",
			currency: "EUR",
		}).format(amount);
	};

	const formatDate = (dateString: string | null) => {
		if (!dateString) return "Aucune limite";
		// Parse date manually to avoid timezone issues
		const [year, month, day] = dateString.split("-").map(Number);
		const dateObj = new Date(year, month - 1, day);
		return dateObj.toLocaleDateString("fr-FR");
	};

	const getDiscountText = (type: string, value: number) => {
		if (type === "percentage") {
			return `${value}%`;
		} else {
			return formatCurrency(value);
		}
	};

	const isExpired = (validUntil: string | null) => {
		if (!validUntil) return false;
		// Parse date manually to avoid timezone issues
		const [year, month, day] = validUntil.split("-").map(Number);
		const validDate = new Date(year, month - 1, day);
		const today = new Date();
		today.setHours(0, 0, 0, 0); // Reset time to start of day for fair comparison
		return validDate < today;
	};

	const getStatusColor = (coupon: DiscountCoupon) => {
		if (!coupon.is_active) return "bg-red-100 text-red-800";
		if (isExpired(coupon.valid_until)) return "bg-orange-100 text-orange-800";
		if (coupon.usage_limit && coupon.current_usage >= coupon.usage_limit) return "bg-gray-100 text-gray-800";
		return "bg-green-100 text-green-800";
	};

	const getStatusText = (coupon: DiscountCoupon) => {
		if (!coupon.is_active) return "Inactif";
		if (isExpired(coupon.valid_until)) return "Expiré";
		if (coupon.usage_limit && coupon.current_usage >= coupon.usage_limit) return "Épuisé";
		return "Actif";
	};

	if (loading) {
		return (
			<div className="p-6">
				<div className="flex items-center justify-center h-64">
					<Loader2 className="h-8 w-8 animate-spin text-emerald-600" />
				</div>
			</div>
		);
	}

	return (
		<div className="p-6">
			<div className="flex justify-between items-center mb-8">
				<div>
					<h1 className="text-3xl font-bold text-gray-900 mb-2">Codes de Réduction</h1>
					<p className="text-gray-600">Gérez vos codes promotionnels et offres spéciales</p>
				</div>
				<Button onClick={handleCreate} icon={Plus}>
					Nouveau Code
				</Button>
			</div>

			{/* Error Message */}
			{error && (
				<div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
					<div className="flex items-center">
						<AlertCircle className="h-5 w-5 text-red-600 mr-2" />
						<span className="text-red-700">{error}</span>
					</div>
				</div>
			)}

			{/* Create/Edit Form */}
			{(isCreating || editingId) && (
				<div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
					<h2 className="text-xl font-bold text-gray-900 mb-6">
						{isCreating ? "Créer un code de réduction" : "Modifier le code de réduction"}
					</h2>

					<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
						<div>
							<label className="block text-sm font-medium text-gray-700 mb-2">Code *</label>
							<input
								type="text"
								value={editForm.code || ""}
								onChange={(e) => handleInputChange("code", e.target.value.toUpperCase())}
								className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
								placeholder="Ex: SUMMER2024, WELCOME10..."
							/>
						</div>

						<div>
							<label className="block text-sm font-medium text-gray-700 mb-2">Type de réduction *</label>
							<select
								value={editForm.discount_type || "percentage"}
								onChange={(e) => handleInputChange("discount_type", e.target.value)}
								className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
							>
								<option value="percentage">Pourcentage</option>
								<option value="fixed">Montant fixe</option>
							</select>
						</div>

						<div>
							<label className="block text-sm font-medium text-gray-700 mb-2">
								Valeur de réduction *
							</label>
							<div className="relative">
								<input
									type="number"
									step="0.01"
									min="0"
									max={editForm.discount_type === "percentage" ? "100" : undefined}
									value={editForm.discount_value || 0}
									onChange={(e) => handleInputChange("discount_value", parseFloat(e.target.value))}
									className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
								/>
								<div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
									{editForm.discount_type === "percentage" ? (
										<Percent className="h-4 w-4 text-gray-400" />
									) : (
										<span className="text-gray-400">€</span>
									)}
								</div>
							</div>
						</div>

						<div>
							<label className="block text-sm font-medium text-gray-700 mb-2">Limite d'utilisation</label>
							<input
								type="number"
								min="1"
								value={editForm.usage_limit || ""}
								onChange={(e) =>
									handleInputChange(
										"usage_limit",
										e.target.value ? parseInt(e.target.value) : undefined
									)
								}
								className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
								placeholder="Laisser vide pour illimité"
							/>
						</div>

						<div>
							<label className="block text-sm font-medium text-gray-700 mb-2">
								Montant minimum d'achat (€)
							</label>
							<input
								type="number"
								step="0.01"
								min="0"
								value={editForm.min_purchase_amount || ""}
								onChange={(e) =>
									handleInputChange(
										"min_purchase_amount",
										e.target.value ? parseFloat(e.target.value) : undefined
									)
								}
								className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
								placeholder="Aucun minimum"
							/>
						</div>

						<div>
							<label className="block text-sm font-medium text-gray-700 mb-2">
								Réduction maximum (€)
							</label>
							<input
								type="number"
								step="0.01"
								min="0"
								value={editForm.max_discount_amount || ""}
								onChange={(e) =>
									handleInputChange(
										"max_discount_amount",
										e.target.value ? parseFloat(e.target.value) : undefined
									)
								}
								className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
								placeholder="Aucune limite"
							/>
						</div>

						<div>
							<label className="block text-sm font-medium text-gray-700 mb-2">Date de début</label>
							<input
								type="date"
								value={editForm.valid_from || ""}
								onChange={(e) => handleInputChange("valid_from", e.target.value)}
								className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
							/>
						</div>

						<div>
							<label className="block text-sm font-medium text-gray-700 mb-2">Date de fin</label>
							<input
								type="date"
								value={editForm.valid_until || ""}
								onChange={(e) => handleInputChange("valid_until", e.target.value)}
								className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
							/>
						</div>

						<div className="md:col-span-2">
							<label className="block text-sm font-medium text-gray-700 mb-2">Description</label>
							<textarea
								value={editForm.description || ""}
								onChange={(e) => handleInputChange("description", e.target.value)}
								rows={3}
								className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
								placeholder="Description du code de réduction..."
							/>
						</div>

						<div>
							<label className="block text-sm font-medium text-gray-700 mb-2">Statut</label>
							<select
								value={editForm.is_active ? "true" : "false"}
								onChange={(e) => handleInputChange("is_active", e.target.value === "true")}
								className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
							>
								<option value="true">Actif</option>
								<option value="false">Inactif</option>
							</select>
						</div>
					</div>

					<div className="flex justify-end gap-4 mt-6 pt-6 border-t">
						<Button variant="outline" onClick={handleCancel} icon={X} disabled={saving}>
							Annuler
						</Button>
						<Button onClick={handleSave} icon={saving ? Loader2 : Save} disabled={saving}>
							{saving ? "Sauvegarde..." : isCreating ? "Créer le code" : "Sauvegarder"}
						</Button>
					</div>
				</div>
			)}

			{/* Coupons List */}
			{!loading && (
				<div className="bg-white rounded-xl shadow-sm border border-gray-200">
					<div className="p-6 border-b border-gray-200">
						<h2 className="text-xl font-bold text-gray-900">Codes de réduction ({coupons.length})</h2>
					</div>
					<div className="divide-y divide-gray-200">
						{coupons.length === 0 ? (
							<div className="p-12 text-center">
								<Tag className="h-12 w-12 text-gray-400 mx-auto mb-4" />
								<p className="text-gray-500">Aucun code de réduction trouvé</p>
							</div>
						) : (
							coupons.map((coupon) => (
								<div key={coupon.id} className="p-6">
									<div className="flex items-start justify-between">
										<div className="flex-1">
											<div className="flex items-center gap-3 mb-2">
												<Tag className="h-5 w-5 text-emerald-600" />
												<h3 className="text-lg font-semibold text-gray-900 font-mono">
													{coupon.code}
												</h3>
												<span
													className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(
														coupon
													)}`}
												>
													{getStatusText(coupon)}
												</span>
											</div>
											{coupon.description && (
												<p className="text-gray-600 text-sm mb-3">{coupon.description}</p>
											)}
											<div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
												<div>
													<span className="text-gray-500">Réduction:</span>
													<p className="font-medium text-emerald-600">
														{getDiscountText(coupon.discount_type, coupon.discount_value)}
													</p>
												</div>
												<div>
													<span className="text-gray-500">Utilisations:</span>
													<p className="font-medium">
														{coupon.current_usage}
														{coupon.usage_limit ? ` / ${coupon.usage_limit}` : " / ∞"}
													</p>
												</div>
												<div>
													<span className="text-gray-500">Valide du:</span>
													<p className="font-medium">{formatDate(coupon.valid_from)}</p>
												</div>
												<div>
													<span className="text-gray-500">Valide jusqu'au:</span>
													<p className="font-medium">{formatDate(coupon.valid_until)}</p>
												</div>
											</div>
										</div>
										<div className="flex gap-2 ml-4">
											<Button
												variant="outline"
												size="sm"
												icon={Edit}
												onClick={() => handleEdit(coupon)}
												disabled={saving}
											>
												Modifier
											</Button>
											<Button
												variant="outline"
												size="sm"
												icon={Trash2}
												onClick={() => handleDelete(coupon.id)}
												className="text-red-600 hover:text-red-700 border-red-200 hover:border-red-300"
												disabled={saving}
											>
												Supprimer
											</Button>
										</div>
									</div>
								</div>
							))
						)}
					</div>
				</div>
			)}
		</div>
	);
};

export default AdminDiscountCoupons;
