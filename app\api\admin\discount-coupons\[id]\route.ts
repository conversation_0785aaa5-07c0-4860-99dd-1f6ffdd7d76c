import { logAdminAction, withAdminAuth } from "@/lib/admin-auth";
import { supabaseAdmin } from "@/lib/supabase";
import { Database } from "@/lib/types";
import { NextRequest, NextResponse } from "next/server";

type DiscountCoupon = Database["public"]["Tables"]["discount_coupons"]["Row"];
type DiscountCouponUpdate = Database["public"]["Tables"]["discount_coupons"]["Update"];

// GET /api/admin/discount-coupons/[id] - Get single discount coupon
export const GET = withAdminAuth(async (request: NextRequest, user, { params }: { params: { id: string } }) => {
	try {
		const couponId = params.id;

		if (!supabaseAdmin) {
			return NextResponse.json({ error: "Database connection failed" }, { status: 500 });
		}

		const { data: discountCoupon, error } = await supabaseAdmin
			.from("discount_coupons")
			.select("*")
			.eq("id", couponId)
			.single();

		if (error) {
			if (error.code === "PGRST116") {
				return NextResponse.json({ error: "Discount coupon not found" }, { status: 404 });
			}
			console.error("Error fetching discount coupon:", error);
			return NextResponse.json({ error: "Failed to fetch discount coupon" }, { status: 500 });
		}

		return NextResponse.json({ discountCoupon });
	} catch (error) {
		console.error("Discount coupon GET error:", error);
		return NextResponse.json({ error: "Internal server error" }, { status: 500 });
	}
}, "services:read");

// PUT /api/admin/discount-coupons/[id] - Update single discount coupon
export const PUT = withAdminAuth(async (request: NextRequest, user, { params }: { params: { id: string } }) => {
	try {
		const couponId = params.id;
		const updates: DiscountCouponUpdate = await request.json();

		// Get current coupon for audit log
		if (!supabaseAdmin) {
			return NextResponse.json({ error: "Database connection failed" }, { status: 500 });
		}

		const { data: currentCoupon } = await supabaseAdmin
			.from("discount_coupons")
			.select("*")
			.eq("id", couponId)
			.single();

		if (!currentCoupon) {
			return NextResponse.json({ error: "Discount coupon not found" }, { status: 404 });
		}

		// Validate discount type and value if provided
		if (updates.discount_type === "percentage" && updates.discount_value && updates.discount_value > 100) {
			return NextResponse.json({ error: "Percentage discount cannot exceed 100%" }, { status: 400 });
		}

		if (updates.discount_value !== undefined && updates.discount_value <= 0) {
			return NextResponse.json({ error: "Discount value must be greater than 0" }, { status: 400 });
		}

		// Check if coupon code already exists (if code is being updated)
		if (updates.code && updates.code !== currentCoupon.code) {
			if (!supabaseAdmin) {
				return NextResponse.json({ error: "Database connection failed" }, { status: 500 });
			}

			const { data: existingCoupon } = await supabaseAdmin
				.from("discount_coupons")
				.select("id")
				.eq("code", updates.code)
				.neq("id", couponId)
				.single();

			if (existingCoupon) {
				return NextResponse.json({ error: "Coupon code already exists" }, { status: 400 });
			}
		}

		// Update discount coupon
		if (!supabaseAdmin) {
			return NextResponse.json({ error: "Database connection failed" }, { status: 500 });
		}

		const { data: updatedCoupon, error } = await supabaseAdmin
			.from("discount_coupons")
			.update(updates)
			.eq("id", couponId)
			.select()
			.single();

		if (error) {
			console.error("Error updating discount coupon:", error);
			return NextResponse.json({ error: "Failed to update discount coupon" }, { status: 500 });
		}

		// Log admin action
		await logAdminAction(user.id, "update", "discount_coupons", couponId, currentCoupon, updatedCoupon, request);

		return NextResponse.json({ discountCoupon: updatedCoupon });
	} catch (error) {
		console.error("Discount coupon PUT error:", error);
		return NextResponse.json({ error: "Internal server error" }, { status: 500 });
	}
}, "services:write");

// DELETE /api/admin/discount-coupons/[id] - Deactivate single discount coupon
export const DELETE = withAdminAuth(async (request: NextRequest, user, { params }: { params: { id: string } }) => {
	try {
		const couponId = params.id;

		// Get coupon for audit log
		if (!supabaseAdmin) {
			return NextResponse.json({ error: "Database connection failed" }, { status: 500 });
		}

		const { data: couponToDeactivate } = await supabaseAdmin
			.from("discount_coupons")
			.select("*")
			.eq("id", couponId)
			.single();

		if (!couponToDeactivate) {
			return NextResponse.json({ error: "Discount coupon not found" }, { status: 404 });
		}

		// Check if this coupon is used in any active reservations
		if (!supabaseAdmin) {
			return NextResponse.json({ error: "Database connection failed" }, { status: 500 });
		}

		const { data: activeReservations } = await supabaseAdmin
			.from("reservations")
			.select("id")
			.eq("discount_code", couponToDeactivate.code)
			.in("status", ["pending", "confirmed"])
			.limit(1);

		if (activeReservations && activeReservations.length > 0) {
			return NextResponse.json(
				{
					error: "Cannot deactivate coupon that is used in active reservations",
				},
				{ status: 400 }
			);
		}

		// Soft delete by setting is_active to false
		if (!supabaseAdmin) {
			return NextResponse.json({ error: "Database connection failed" }, { status: 500 });
		}

		const { error } = await supabaseAdmin.from("discount_coupons").update({ is_active: false }).eq("id", couponId);

		if (error) {
			console.error("Error deactivating discount coupon:", error);
			return NextResponse.json({ error: "Failed to deactivate discount coupon" }, { status: 500 });
		}

		// Log admin action
		await logAdminAction(
			user.id,
			"delete",
			"discount_coupons",
			couponId,
			couponToDeactivate,
			{
				...couponToDeactivate,
				is_active: false,
			},
			request
		);

		return NextResponse.json({ message: "Discount coupon deactivated successfully" });
	} catch (error) {
		console.error("Discount coupon DELETE error:", error);
		return NextResponse.json({ error: "Internal server error" }, { status: 500 });
	}
}, "services:write");
